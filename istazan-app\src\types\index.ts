// أنواع البيانات الأساسية لتطبيق استأذن

export interface Student {
  name: string;
  grade: string;
}

export interface PermissionRequest {
  id?: string;
  parentName: string;
  relationship: string;
  students: Student[];
  status: 'pending' | 'approved' | 'rejected' | 'processed';
  createdAt: Date;
  processedAt?: Date;
  processedBy?: string;
  notes?: string;
}

export interface Grade {
  id: string;
  name: string;
  level: number;
}

export interface User {
  id: string;
  name: string;
  role: 'parent' | 'principal' | 'vice_principal' | 'student_affairs';
  email?: string;
  phone?: string;
}

export interface Notification {
  id: string;
  requestId: string;
  message: string;
  sentAt: Date;
  readAt?: Date;
}

export type RelationshipType = 
  | 'father' 
  | 'mother' 
  | 'brother' 
  | 'sister' 
  | 'uncle' 
  | 'aunt' 
  | 'grandfather' 
  | 'grandmother' 
  | 'guardian';

export const RELATIONSHIP_LABELS: Record<RelationshipType, string> = {
  father: 'الأب',
  mother: 'الأم',
  brother: 'الأخ',
  sister: 'الأخت',
  uncle: 'العم',
  aunt: 'العمة',
  grandfather: 'الجد',
  grandmother: 'الجدة',
  guardian: 'الوصي'
};

export const GRADE_LEVELS = [
  { id: 'first', name: 'الأول متوسط', level: 1 },
  { id: 'second', name: 'الثاني متوسط', level: 2 },
  { id: 'third', name: 'الثالث متوسط', level: 3 }
];
