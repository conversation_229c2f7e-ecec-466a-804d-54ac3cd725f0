const moment = require('moment-timezone');

// Middleware to validate request time
const validateRequestTime = (req, res, next) => {
  try {
    // Get current time in Saudi Arabia timezone
    const now = moment().tz('Asia/Riyadh');
    const currentHour = now.hour();
    const currentMinute = now.minute();
    
    // Permission requests allowed after 11:00 AM
    const allowedHour = 11;
    const allowedMinute = 0;
    
    // Check if current time is before 11:00 AM
    if (currentHour < allowedHour || (currentHour === allowedHour && currentMinute < allowedMinute)) {
      return res.status(400).json({
        error: 'وقت غير مسموح',
        message: 'يمكن تقديم طلبات الاستئذان من الساعة 11:00 صباحاً فقط',
        currentTime: now.format('HH:mm'),
        allowedTime: '11:00'
      });
    }
    
    // Add current time to request for logging
    req.requestTime = now.toISOString();
    next();
  } catch (error) {
    console.error('خطأ في التحقق من الوقت:', error);
    res.status(500).json({
      error: 'خطأ في التحقق من الوقت',
      message: 'حدث خطأ أثناء التحقق من وقت الطلب'
    });
  }
};

// Function to check if current time allows requests
const isRequestTimeAllowed = () => {
  const now = moment().tz('Asia/Riyadh');
  const currentHour = now.hour();
  const currentMinute = now.minute();
  
  return currentHour > 11 || (currentHour === 11 && currentMinute >= 0);
};

// Get current time info
const getCurrentTimeInfo = () => {
  const now = moment().tz('Asia/Riyadh');
  return {
    currentTime: now.format('HH:mm'),
    currentDate: now.format('YYYY-MM-DD'),
    isAllowed: isRequestTimeAllowed(),
    allowedTime: '11:00'
  };
};

module.exports = {
  validateRequestTime,
  isRequestTimeAllowed,
  getCurrentTimeInfo
};
