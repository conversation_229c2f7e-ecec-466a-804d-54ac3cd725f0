'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Users, UserCheck, GraduationCap, Clock } from 'lucide-react'

export default function HomePage() {
  const [currentTime] = useState(new Date())

  const userTypes = [
    {
      title: 'ولي الأمر',
      description: 'تقديم طلب استئذان لابنك/ابنتك',
      icon: Users,
      href: '/parent',
      color: 'bg-primary-500 hover:bg-primary-600'
    },
    {
      title: 'المدير',
      description: 'مراجعة جميع طلبات الاستئذان',
      icon: UserCheck,
      href: '/principal',
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      title: 'الوكيل',
      description: 'معالجة طلبات الاستئذان',
      icon: GraduationCap,
      href: '/vice-principal',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      title: 'شاشات الصفوف',
      description: 'عرض طلبات الاستئذان حسب الصف',
      icon: Clock,
      href: '/grades',
      color: 'bg-purple-500 hover:bg-purple-600'
    }
  ]

  return (
    <div className="max-w-4xl mx-auto">
      {/* ترحيب */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-secondary-800 mb-4">
          مرحباً بك في نظام استأذن
        </h1>
        <p className="text-lg text-secondary-600 mb-6">
          نظام رقمي متطور لإدارة طلبات الاستئذان المدرسي بكفاءة وسهولة
        </p>
        <div className="bg-white rounded-lg shadow-md p-4 inline-block">
          <div className="flex items-center gap-2 text-secondary-700">
            <Clock className="w-5 h-5" />
            <span>الوقت الحالي: {currentTime.toLocaleTimeString('ar-SA')}</span>
          </div>
          <p className="text-sm text-secondary-500 mt-1">
            يمكن تقديم طلبات الاستئذان من الساعة 11:00 صباحاً
          </p>
        </div>
      </div>

      {/* خيارات المستخدمين */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
        {userTypes.map((userType) => {
          const IconComponent = userType.icon
          return (
            <Link
              key={userType.title}
              href={userType.href}
              className={`${userType.color} text-white rounded-lg p-6 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl`}
            >
              <div className="flex items-center gap-4">
                <IconComponent className="w-12 h-12" />
                <div>
                  <h3 className="text-xl font-bold mb-2">{userType.title}</h3>
                  <p className="text-white/90">{userType.description}</p>
                </div>
              </div>
            </Link>
          )
        })}
      </div>

      {/* معلومات إضافية */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-secondary-800 mb-4">
          كيفية استخدام النظام
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold text-primary-700 mb-2">
              لأولياء الأمور:
            </h3>
            <ul className="text-secondary-600 space-y-1">
              <li>• اختر صلة القرابة</li>
              <li>• أدخل اسمك واسم الطالب</li>
              <li>• حدد الصف الدراسي</li>
              <li>• اضغط إرسال الطلب</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-primary-700 mb-2">
              للإدارة:
            </h3>
            <ul className="text-secondary-600 space-y-1">
              <li>• مراجعة الطلبات الواردة</li>
              <li>• تصنيف حسب الصف</li>
              <li>• معالجة الطلبات</li>
              <li>• إرسال إشعارات للأولياء</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
