const express = require('express');
const { body, validationResult } = require('express-validator');
const PermissionRequest = require('../models/PermissionRequest');
const { validateRequestTime, getCurrentTimeInfo } = require('../middleware/timeValidation');

const router = express.Router();

// Validation rules for permission request
const requestValidation = [
  body('parentName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('اسم ولي الأمر يجب أن يكون بين 2 و 50 حرف'),
  
  body('relationship')
    .isIn(['father', 'mother', 'brother', 'sister', 'uncle', 'aunt', 'grandfather', 'grandmother', 'guardian'])
    .withMessage('صلة القرابة غير صحيحة'),
  
  body('students')
    .isArray({ min: 1, max: 5 })
    .withMessage('يجب إدخال طالب واحد على الأقل وحد أقصى 5 طلاب'),
  
  body('students.*.name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('اسم الطالب يجب أن يكون بين 2 و 50 حرف'),
  
  body('students.*.grade')
    .isIn(['first', 'second', 'third'])
    .withMessage('الصف الدراسي غير صحيح')
];

// Get current time info
router.get('/time-info', (req, res) => {
  try {
    const timeInfo = getCurrentTimeInfo();
    res.json(timeInfo);
  } catch (error) {
    res.status(500).json({ error: 'خطأ في جلب معلومات الوقت' });
  }
});

// Create new permission request
router.post('/', validateRequestTime, requestValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'بيانات غير صحيحة',
        details: errors.array()
      });
    }

    const { parentName, relationship, students } = req.body;

    // Create the request
    const newRequest = await PermissionRequest.create({
      parentName,
      relationship,
      students
    });

    // Emit real-time notification to all connected clients
    const io = req.app.get('io');
    io.emit('new-request', {
      id: newRequest.id,
      parentName: newRequest.parent_name,
      students: newRequest.students,
      createdAt: newRequest.created_at
    });

    // Emit to specific grade rooms
    students.forEach(student => {
      io.to(`grade-${student.grade}`).emit('new-grade-request', {
        id: newRequest.id,
        parentName: newRequest.parent_name,
        student: student,
        createdAt: newRequest.created_at
      });
    });

    res.status(201).json({
      success: true,
      message: 'تم إرسال طلب الاستئذان بنجاح',
      request: newRequest
    });

  } catch (error) {
    console.error('خطأ في إنشاء الطلب:', error);
    res.status(500).json({
      error: 'خطأ في إرسال الطلب',
      message: error.message
    });
  }
});

// Get all requests (for admin/principal)
router.get('/', async (req, res) => {
  try {
    const { status, grade, date } = req.query;
    const filters = {};
    
    if (status) filters.status = status;
    if (grade) filters.grade = grade;
    if (date) filters.date = date;

    const requests = await PermissionRequest.getAll(filters);
    res.json(requests);
  } catch (error) {
    console.error('خطأ في جلب الطلبات:', error);
    res.status(500).json({
      error: 'خطأ في جلب الطلبات',
      message: error.message
    });
  }
});

// Get requests by grade
router.get('/grade/:grade', async (req, res) => {
  try {
    const { grade } = req.params;
    
    if (!['first', 'second', 'third'].includes(grade)) {
      return res.status(400).json({ error: 'الصف الدراسي غير صحيح' });
    }

    const requests = await PermissionRequest.getByGrade(grade);
    res.json(requests);
  } catch (error) {
    console.error('خطأ في جلب طلبات الصف:', error);
    res.status(500).json({
      error: 'خطأ في جلب طلبات الصف',
      message: error.message
    });
  }
});

// Update request status (for vice principal)
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, processedBy, notes } = req.body;

    if (!['pending', 'processed', 'rejected'].includes(status)) {
      return res.status(400).json({ error: 'حالة الطلب غير صحيحة' });
    }

    const updatedRequest = await PermissionRequest.updateStatus(id, status, processedBy, notes);

    // Emit real-time notification
    const io = req.app.get('io');
    io.emit('request-updated', {
      id: updatedRequest.id,
      status: updatedRequest.status,
      processedBy: updatedRequest.processed_by,
      processedAt: updatedRequest.processed_at
    });

    res.json({
      success: true,
      message: 'تم تحديث حالة الطلب بنجاح',
      request: updatedRequest
    });

  } catch (error) {
    console.error('خطأ في تحديث الطلب:', error);
    res.status(500).json({
      error: 'خطأ في تحديث الطلب',
      message: error.message
    });
  }
});

// Get request by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const request = await PermissionRequest.getById(id);
    
    if (!request) {
      return res.status(404).json({ error: 'الطلب غير موجود' });
    }

    res.json(request);
  } catch (error) {
    console.error('خطأ في جلب الطلب:', error);
    res.status(500).json({
      error: 'خطأ في جلب الطلب',
      message: error.message
    });
  }
});

// Get statistics
router.get('/stats/summary', async (req, res) => {
  try {
    const stats = await PermissionRequest.getStats();
    res.json(stats);
  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    res.status(500).json({
      error: 'خطأ في جلب الإحصائيات',
      message: error.message
    });
  }
});

module.exports = router;
