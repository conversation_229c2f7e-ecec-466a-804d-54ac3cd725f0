const express = require('express');
const PermissionRequest = require('../models/PermissionRequest');

const router = express.Router();

// Grade information
const GRADES = {
  first: { id: 'first', name: 'الأول متوسط', level: 1 },
  second: { id: 'second', name: 'الثاني متوسط', level: 2 },
  third: { id: 'third', name: 'الثالث متوسط', level: 3 }
};

// Get all grades
router.get('/', (req, res) => {
  res.json(Object.values(GRADES));
});

// Get specific grade info
router.get('/:gradeId', (req, res) => {
  const { gradeId } = req.params;
  
  if (!GRADES[gradeId]) {
    return res.status(404).json({ error: 'الصف غير موجود' });
  }
  
  res.json(GRADES[gradeId]);
});

// Get requests for specific grade (for grade display screens)
router.get('/:gradeId/requests', async (req, res) => {
  try {
    const { gradeId } = req.params;
    
    if (!GRADES[gradeId]) {
      return res.status(404).json({ error: 'الصف غير موجود' });
    }

    const requests = await PermissionRequest.getByGrade(gradeId);
    
    // Filter to show only pending and processed requests for the day
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayRequests = requests.filter(request => {
      const requestDate = new Date(request.created_at);
      requestDate.setHours(0, 0, 0, 0);
      return requestDate.getTime() === today.getTime();
    });

    res.json({
      grade: GRADES[gradeId],
      requests: todayRequests,
      count: todayRequests.length
    });

  } catch (error) {
    console.error('خطأ في جلب طلبات الصف:', error);
    res.status(500).json({
      error: 'خطأ في جلب طلبات الصف',
      message: error.message
    });
  }
});

// Get grade statistics
router.get('/:gradeId/stats', async (req, res) => {
  try {
    const { gradeId } = req.params;
    
    if (!GRADES[gradeId]) {
      return res.status(404).json({ error: 'الصف غير موجود' });
    }

    const allRequests = await PermissionRequest.getByGrade(gradeId);
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayRequests = allRequests.filter(request => {
      const requestDate = new Date(request.created_at);
      requestDate.setHours(0, 0, 0, 0);
      return requestDate.getTime() === today.getTime();
    });

    const stats = {
      grade: GRADES[gradeId],
      total: allRequests.length,
      today: todayRequests.length,
      pending: todayRequests.filter(r => r.status === 'pending').length,
      processed: todayRequests.filter(r => r.status === 'processed').length,
      rejected: todayRequests.filter(r => r.status === 'rejected').length
    };

    res.json(stats);

  } catch (error) {
    console.error('خطأ في جلب إحصائيات الصف:', error);
    res.status(500).json({
      error: 'خطأ في جلب إحصائيات الصف',
      message: error.message
    });
  }
});

module.exports = router;
