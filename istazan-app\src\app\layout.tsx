import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'استأذن - نظام طلبات الاستئذان المدرسي',
  description: 'نظام رقمي لإدارة طلبات الاستئذان من المدرسة',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className="font-arabic">
        <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100">
          <header className="bg-white shadow-sm border-b border-secondary-200">
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold text-primary-700">
                  استأذن
                </h1>
                <p className="text-secondary-600 text-sm">
                  نظام طلبات الاستئذان المدرسي
                </p>
              </div>
            </div>
          </header>
          
          <main className="container mx-auto px-4 py-8">
            {children}
          </main>
          
          <footer className="bg-secondary-800 text-white py-6 mt-12">
            <div className="container mx-auto px-4 text-center">
              <p className="text-secondary-300">
                © 2025 نظام استأذن - جميع الحقوق محفوظة
              </p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
}
