{"name": "istazan-app", "version": "1.0.0", "description": "نظام طلبات الاستئذان المدرسي", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["school", "permission", "requests", "nodejs", "express"], "author": "School System", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.45.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "socket.io": "^4.7.4", "moment": "^2.29.4", "moment-timezone": "^0.5.43"}, "devDependencies": {"nodemon": "^3.0.2"}}