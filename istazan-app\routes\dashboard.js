const express = require('express');
const PermissionRequest = require('../models/PermissionRequest');

const router = express.Router();

// Principal dashboard - get all requests with statistics
router.get('/principal', async (req, res) => {
  try {
    const { date, status, grade } = req.query;
    
    // Get filtered requests
    const filters = {};
    if (date) filters.date = date;
    if (status) filters.status = status;
    if (grade) filters.grade = grade;
    
    const requests = await PermissionRequest.getAll(filters);
    const stats = await PermissionRequest.getStats();
    
    // Group requests by grade
    const requestsByGrade = {
      first: requests.filter(r => r.students.some(s => s.grade === 'first')),
      second: requests.filter(r => r.students.some(s => s.grade === 'second')),
      third: requests.filter(r => r.students.some(s => s.grade === 'third'))
    };

    res.json({
      stats,
      requests,
      requestsByGrade,
      filters: { date, status, grade }
    });

  } catch (error) {
    console.error('خطأ في لوحة المدير:', error);
    res.status(500).json({
      error: 'خطأ في جلب بيانات لوحة المدير',
      message: error.message
    });
  }
});

// Vice principal dashboard - get pending requests for processing
router.get('/vice-principal', async (req, res) => {
  try {
    const { grade } = req.query;
    
    const filters = { status: 'pending' };
    if (grade) filters.grade = grade;
    
    const pendingRequests = await PermissionRequest.getAll(filters);
    const stats = await PermissionRequest.getStats();
    
    // Group by grade for easier processing
    const requestsByGrade = {
      first: pendingRequests.filter(r => r.students.some(s => s.grade === 'first')),
      second: pendingRequests.filter(r => r.students.some(s => s.grade === 'second')),
      third: pendingRequests.filter(r => r.students.some(s => s.grade === 'third'))
    };

    res.json({
      pendingRequests,
      requestsByGrade,
      stats: {
        pending: stats.pending,
        processed: stats.processed,
        today: stats.today
      }
    });

  } catch (error) {
    console.error('خطأ في لوحة الوكيل:', error);
    res.status(500).json({
      error: 'خطأ في جلب بيانات لوحة الوكيل',
      message: error.message
    });
  }
});

// Student affairs dashboard - general overview
router.get('/student-affairs', async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const todayRequests = await PermissionRequest.getAll({ date: today });
    const stats = await PermissionRequest.getStats();
    
    // Recent activity (last 10 requests)
    const recentRequests = await PermissionRequest.getAll();
    const recentActivity = recentRequests.slice(0, 10);

    res.json({
      todayRequests,
      recentActivity,
      stats,
      summary: {
        totalToday: todayRequests.length,
        pendingToday: todayRequests.filter(r => r.status === 'pending').length,
        processedToday: todayRequests.filter(r => r.status === 'processed').length
      }
    });

  } catch (error) {
    console.error('خطأ في لوحة شؤون الطلاب:', error);
    res.status(500).json({
      error: 'خطأ في جلب بيانات لوحة شؤون الطلاب',
      message: error.message
    });
  }
});

// Get dashboard statistics for any role
router.get('/stats', async (req, res) => {
  try {
    const stats = await PermissionRequest.getStats();
    
    // Additional statistics
    const today = new Date().toISOString().split('T')[0];
    const todayRequests = await PermissionRequest.getAll({ date: today });
    
    const gradeStats = {
      first: todayRequests.filter(r => r.students.some(s => s.grade === 'first')).length,
      second: todayRequests.filter(r => r.students.some(s => s.grade === 'second')).length,
      third: todayRequests.filter(r => r.students.some(s => s.grade === 'third')).length
    };

    res.json({
      ...stats,
      gradeStats,
      todayTotal: todayRequests.length
    });

  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    res.status(500).json({
      error: 'خطأ في جلب الإحصائيات',
      message: error.message
    });
  }
});

module.exports = router;
