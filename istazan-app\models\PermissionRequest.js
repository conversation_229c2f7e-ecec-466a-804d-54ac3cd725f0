const { supabase } = require('../config/supabase');

class PermissionRequest {
  constructor(data) {
    this.parentName = data.parentName;
    this.relationship = data.relationship;
    this.students = data.students; // Array of {name, grade}
    this.status = data.status || 'pending';
    this.createdAt = data.createdAt || new Date();
    this.processedAt = data.processedAt;
    this.processedBy = data.processedBy;
    this.notes = data.notes;
  }

  // Create new permission request
  static async create(requestData) {
    try {
      const { data, error } = await supabase
        .from('permission_requests')
        .insert([{
          parent_name: requestData.parentName,
          relationship: requestData.relationship,
          students: requestData.students,
          status: 'pending',
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`خطأ في إنشاء الطلب: ${error.message}`);
    }
  }

  // Get all requests
  static async getAll(filters = {}) {
    try {
      let query = supabase
        .from('permission_requests')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      
      if (filters.grade) {
        query = query.contains('students', [{ grade: filters.grade }]);
      }

      if (filters.date) {
        const startOfDay = new Date(filters.date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(filters.date);
        endOfDay.setHours(23, 59, 59, 999);
        
        query = query
          .gte('created_at', startOfDay.toISOString())
          .lte('created_at', endOfDay.toISOString());
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`خطأ في جلب الطلبات: ${error.message}`);
    }
  }

  // Get requests by grade
  static async getByGrade(grade) {
    try {
      const { data, error } = await supabase
        .from('permission_requests')
        .select('*')
        .contains('students', [{ grade: grade }])
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`خطأ في جلب طلبات الصف: ${error.message}`);
    }
  }

  // Update request status
  static async updateStatus(id, status, processedBy = null, notes = null) {
    try {
      const updateData = {
        status: status,
        processed_at: new Date().toISOString()
      };

      if (processedBy) updateData.processed_by = processedBy;
      if (notes) updateData.notes = notes;

      const { data, error } = await supabase
        .from('permission_requests')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`خطأ في تحديث حالة الطلب: ${error.message}`);
    }
  }

  // Get request by ID
  static async getById(id) {
    try {
      const { data, error } = await supabase
        .from('permission_requests')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`خطأ في جلب الطلب: ${error.message}`);
    }
  }

  // Get statistics
  static async getStats() {
    try {
      const { data, error } = await supabase
        .from('permission_requests')
        .select('status, created_at');

      if (error) throw error;

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const stats = {
        total: data.length,
        pending: data.filter(r => r.status === 'pending').length,
        processed: data.filter(r => r.status === 'processed').length,
        today: data.filter(r => new Date(r.created_at) >= today).length
      };

      return stats;
    } catch (error) {
      throw new Error(`خطأ في جلب الإحصائيات: ${error.message}`);
    }
  }
}

module.exports = PermissionRequest;
