-- Supabase Database Schema for استأذن App
-- Run these commands in your Supabase SQL Editor

-- Create permission_requests table
CREATE TABLE permission_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    parent_name VARCHAR(100) NOT NULL,
    relationship VARCHAR(20) NOT NULL CHECK (relationship IN ('father', 'mother', 'brother', 'sister', 'uncle', 'aunt', 'grandfather', 'grandmother', 'guardian')),
    students JSONB NOT NULL, -- Array of {name: string, grade: string}
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'rejected')),
    notes TEXT,
    processed_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    request_id UUID REFERENCES permission_requests(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Create users table (optional for future authentication)
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('parent', 'principal', 'vice_principal', 'student_affairs')),
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_permission_requests_status ON permission_requests(status);
CREATE INDEX idx_permission_requests_created_at ON permission_requests(created_at);
CREATE INDEX idx_permission_requests_students ON permission_requests USING GIN(students);
CREATE INDEX idx_notifications_request_id ON notifications(request_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_permission_requests_updated_at 
    BEFORE UPDATE ON permission_requests 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE permission_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed for your security requirements)
CREATE POLICY "Allow all operations on permission_requests" ON permission_requests
    FOR ALL USING (true);

CREATE POLICY "Allow all operations on notifications" ON notifications
    FOR ALL USING (true);

CREATE POLICY "Allow all operations on users" ON users
    FOR ALL USING (true);

-- Insert sample data for testing
INSERT INTO permission_requests (parent_name, relationship, students, status) VALUES
('أحمد محمد', 'father', '[{"name": "محمد أحمد", "grade": "first"}]', 'pending'),
('فاطمة علي', 'mother', '[{"name": "علي فاطمة", "grade": "second"}]', 'processed'),
('سعد الله', 'father', '[{"name": "نور سعد", "grade": "third"}, {"name": "أمل سعد", "grade": "first"}]', 'pending');
